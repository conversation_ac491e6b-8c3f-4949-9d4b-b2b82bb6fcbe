#define STB_IMAGE_IMPLEMENTATION
#define STB_ONLY_PNG
#include "stb_image.h"
#include "engine.h"

#include <emscripten.h>
#include <emscripten/console.h>
#include <emscripten/key_codes.h>
#include <emscripten/html5.h>


#ifdef DEBUG
#define LogInfo emscripten_console_logf
#define LogWarn emscripten_console_warnf
#define LogError emscripten_console_errorf
#else
#define LogInfo(...)
#define LogWarn(...)
#define LogError emscripten_console_errorf
#endif

typedef struct {
    bool IsDown;
    bool WasDown
} ButtonState;

typedef union {
    ButtonState Buttons[6];
    struct {
        ButtonState MoveUp;
        ButtonState MoveRight;
        ButtonState MoveDown;
        ButtonState MoveLeft;
        ButtonState Start;
        ButtonState Back;
    };
} KeyboardState;

typedef struct {
    float X, Y;
    ButtonState LeftButton;
    ButtonState RightButton;
} MouseState;

typedef struct {
    uint64_t Tick;
    double LastUpdateTime;
    double TargetUpdateAccum;
    double TargetUpdateRate;
    KeyboardState Keyboard;
    MouseState Mouse;
} PlatformState;

static PlatformState GlobalPlatformState;

static void
WebShowError(const char* Message)
{
    EM_ASM({
        document.getElementById("error-message").textContent = UTF8ToString($0);
        document.getElementById("error-dialog").showModal();
    }, Message);
}

static EM_BOOL
WebKeyboardCallback(int EventType, const EmscriptenKeyboardEvent* Event, void* Data)
{
    KeyboardState* State = (KeyboardState*)Data;

    if (EventType == EMSCRIPTEN_EVENT_KEYDOWN)
    {
        switch (Event->keyCode)
        {
            case DOM_VK_W:
            case DOM_VK_UP:
                State->MoveUp.IsDown = true;
                break;
            case DOM_VK_S:
            case DOM_VK_DOWN:
                State->MoveDown.IsDown = true;
                break;
            case DOM_VK_A:
            case DOM_VK_LEFT:
                State->MoveLeft.IsDown = true;
                break;
            case DOM_VK_D:
            case DOM_VK_RIGHT:
                State->MoveRight.IsDown = true;
                break;
            case DOM_VK_ENTER:
            case DOM_VK_SPACE:
                State->Start.IsDown = true;
                break;
            case DOM_VK_ESCAPE:
                State->Back.IsDown = true;
                break;
        }
    }
    else if (EventType == EMSCRIPTEN_EVENT_KEYUP)
    {
        switch (Event->keyCode)
        {
            case DOM_VK_W:
            case DOM_VK_UP:
                State->MoveUp.IsDown = false;
                break;
            case DOM_VK_S:
            case DOM_VK_DOWN:
                State->MoveDown.IsDown = false;
                break;
            case DOM_VK_A:
            case DOM_VK_LEFT:
                State->MoveLeft.IsDown = false;
                break;
            case DOM_VK_D:
            case DOM_VK_RIGHT:
                State->MoveRight.IsDown = false;
                break;
            case DOM_VK_ENTER:
            case DOM_VK_SPACE:
                State->Start.IsDown = false;
                break;
            case DOM_VK_ESCAPE:
                State->Back.IsDown = false;
                break;
        }
    }

    return EM_TRUE;
}

static EM_BOOL
WebMouseCallback(int EventType, const EmscriptenMouseEvent* Event, void* Data)
{
    MouseState* State = (MouseState*)Data;

    State->X = Event->targetX;
    State->Y = Event->targetY;

    if (EventType == EMSCRIPTEN_EVENT_MOUSEDOWN)
    {
        switch (Event->button)
        {
            case 0:
                State->LeftButton.IsDown = true;
                break;
            case 2:
                State->RightButton.IsDown = true;
                break;
        }
    }
    else if (EventType == EMSCRIPTEN_EVENT_MOUSEUP)
    {
        switch (Event->button)
        {
            case 0:
                State->LeftButton.IsDown = false;
                break;
            case 2:
                State->RightButton.IsDown = false;
                break;
        }
    }

    return EM_TRUE;
}

static void
WebMainLoop(void* Data)
{
    PlatformState* State = (PlatformState*)Data;

    double CurrentTime = emscripten_get_now();

    State->TargetUpdateAccum += CurrentTime - State->LastUpdateTime;
    State->LastUpdateTime = CurrentTime;

    while (State->TargetUpdateAccum > State->TargetUpdateRate)
    {
        State->PlatformState.tick += 1;

        // game_update_and_render(&platform_state);

        State->TargetUpdateAccum -= State->TargetUpdateRate;

        for (size_t KeyIndex = 0; KeyIndex < ArrayCount(State->Keyboard.Buttons); ++KeyIndex)
        {
            State->Keyboard.Buttons[KeyIndex].WasDown = State->Keyboard.Buttons[KeyIndex].IsDown;
        }

        State->Mouse.Left.WasDown = State->Mouse.Left.IsDown;
        State->Mouse.Right.WasDown = State->Mouse.Right.IsDown;
    }
}

int
main(void)
{
    PlatformState PlatformState = {0};
    PlatformState.TargetUpdateRate = 1000.0 / 60.0;

    EMSCRIPTEN_RESULT Result =
        emscripten_set_keydown_callback(EMSCRIPTEN_EVENT_TARGET_WINDOW,
                                        &PlatformState.Keyboard,
                                        EM_FALSE,
                                        WebKeyboardCallback);

    if (Result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        LogError("Failed to set keydown callback: %d", result);
        WebShowError("The game has encountered a fatal error!");
        return 1;
    }

    Result = emscripten_set_keyup_callback(EMSCRIPTEN_EVENT_TARGET_WINDOW,
                                           &PlatformState.Keyboard,
                                           EM_FALSE,
                                           WebKeyboardCallback);

    if (Result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        WebShowError("The game has encountered a fatal error!");
        LogError("Failed to set keyup callback: %d", result);
        return 1;
    }

    Result = emscripten_set_mousedown_callback("#canvas", &PlatformState.Mouse,
                                               EM_FALSE, WebMouseCallback);

    if (Result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        WebShowError("The game has encountered a fatal error!");
        LogError("Failed to set mousedown callback: %d", result);
        return 1;
    }

    Result = emscripten_set_mouseup_callback("#canvas", &PlatformState.Mouse,
                                             EM_FALSE, WebMouseCallback);

    if (Result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        WebShowError("The game has encountered a fatal error!");
        LogError("Failed to set mouseup callback: %d", result);
        return 1;
    }

    Result = emscripten_set_mousemove_callback("#canvas", &PlatformState.Mouse,
                                               EM_FALSE, WebMouseCallback);

    if (Result != EMSCRIPTEN_RESULT_SUCCESS)
    {
        WebShowError("The game has encountered a fatal error!");
        LogError("Failed to set mousemove callback: %d", result);
        return 1;
    }

    emscripten_set_main_loop_arg(WebMainLoop, &PlatformState, 0, EM_TRUE);
    
    return 0;
}